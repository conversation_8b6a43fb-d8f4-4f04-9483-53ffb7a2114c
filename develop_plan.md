SchedSpot – Booking & Service Marketplace Plugin
The chosen name SchedSpot combines “schedule” and “spot,” evoking a modern platform for scheduling services. It’s short, easy to pronounce, and globally neutral. (We verified via domain lookup that schedspot.com is available at press time.) This brandable name suggests a central “spot” for clients and workers to meet and book services, aligning well with the Amelia/TaskRabbit inspiration.

## DEVELOPMENT STATUS: v1.0 FULL RELEASE IN PROGRESS 🚧

**v0.1 MVP - COMPLETED ✅**
- ✅ Core plugin initialization and bootstrap
- ✅ Custom database tables for bookings, services, workers
- ✅ User roles and capabilities (Customer, Worker, Admin)
- ✅ Booking data model with CRUD operations
- ✅ Frontend shortcodes ([schedspot_booking_form], [schedspot_service_list], [schedspot_dashboard])
- ✅ Admin interface with dashboard, bookings management, and settings
- ✅ Basic form validation and AJAX functionality
- ✅ Responsive CSS styling for all components

**v1.0 Full Release - COMPLETED ✅**
- ✅ **REST API Endpoints** - Complete `/wp-json/schedspot/v1/` API with bookings, services, workers, availability endpoints
- ✅ **Enhanced Services Management** - Full CRUD interface for services with worker assignment and pricing
- ✅ **Worker Management System** - Comprehensive worker profiles, availability scheduling, and enhanced dashboard
- ✅ **WooCommerce Integration** - Payment processing, order management, and earnings tracking
- ✅ **Google Calendar Sync** - Two-way calendar synchronization with OAuth 2.0 authentication

**Current Status:** v1.0 Full Release completed! All core features verified and functional. v2.0+ SMS integration completed.

**COMPREHENSIVE IMPLEMENTATION REVIEW COMPLETED ✅ (June 22, 2025)**

**Critical Bug Fixes Applied:**
- ✅ **SMS Authentication Method** - Fixed missing `sms_authenticate` method causing fatal errors
- ✅ **Plugin Syntax Validation** - All core files pass PHP syntax checks
- ✅ **Error Log Cleanup** - Resolved all fatal errors and warnings

**v2.0+ Extensions - IN PROGRESS 🚧**
- ✅ **SMS Login and Notifications** - Complete Twilio integration with 2FA and booking notifications (VERIFIED FUNCTIONAL)
- ✅ **Geofencing (Location Services)** - Geographic service restrictions and distance calculations (COMPLETED ✅)
- 🚧 **Messaging System** - Private messaging between clients and workers (NEXT)
- 🚧 **Frontend Theming & Templates** - Customizable templates and theme compatibility

**IMPLEMENTATION STATUS SUMMARY:**
✅ **Backend**: All PHP classes properly structured with SchedSpot_ prefix
✅ **Database**: All CRUD operations functional for bookings, services, workers
✅ **WordPress Hooks**: All actions/filters properly registered and functional
✅ **Error Handling**: Comprehensive validation and error handling implemented
✅ **API**: Complete REST API with all endpoints functional (/wp-json/schedspot/v1/)
✅ **Authentication**: Proper permission checks on all endpoints
✅ **Response Formats**: Valid JSON responses with proper error handling
✅ **Frontend**: All shortcodes render correctly without errors
✅ **AJAX**: Functional AJAX for forms and dynamic content
✅ **UI Components**: Booking forms, dashboards, and management pages working
✅ **Scripts/Styles**: Proper enqueuing with no console errors
✅ **Google Calendar**: OAuth 2.0, event creation/updates, two-way sync functional
✅ **SMS Integration**: Twilio API, verification codes, notifications working
✅ **WooCommerce**: Product creation, payment processing, commission calculations functional
✅ **Error Handling**: All third-party API calls handle errors gracefully
Setup Phase
Environment & Tools: Set up a local WordPress development environment (PHP, MySQL) and Node (for React/Gutenberg). Use a version control system (e.g. Git) with coding-standard linting (WordPress/PHP-CS, ESLint). Install WP-CLI for scaffolding.
Repository: Create a new repository named schedspot with an appropriate plugin header file. Configure automated builds (e.g. npm scripts or [@wordpress/scripts]{12†L108-L116} for React builds and linting).
Folder Structure: Follow WordPress plugin best practices: one main PHP file in the plugin root, plus subfolders for includes/, admin/, public/, assets/, etc
developer.wordpress.org
. For example, put all admin-facing PHP classes/forms in admin/ and public front-end code in public/. Use class-based design for large modules
developer.wordpress.org
. This separation (often via is_admin() checks) ensures clean code organization
developer.wordpress.org
.
Dependencies: Define dependencies in composer.json (for any PHP packages) and package.json (for Node modules). Include @wordpress packages (@wordpress/components, @wordpress/scripts etc
developer.wordpress.org
developer.wordpress.org
) for Gutenberg/React, as well as any libraries for maps (Leaflet/GoogleMaps) and SMS (e.g. Twilio SDK).
Core Modules Development
Develop the core backend and frontend in parallel, with clear interfaces. Key modules include:
Data Models: Define PHP classes (or custom post types/tables) for Services, Bookings, Workers, Clients, and Locations. For example, a Booking class with methods to check conflicts, status, and notifications. Separate business logic (classes) from presentation (templates)
developer.woocommerce.com
.
REST API: Register custom REST endpoints (e.g. /wp-json/schedspot/v1/bookings, /workers, /services) using register_rest_route(). Follow WordPress standards to handle requests, permissions and responses. Document each endpoint clearly. Use WordPress actions and filters to allow extensions
developer.woocommerce.com
.
Frontend (React & Gutenberg): Build React-based dashboards (see below) and Gutenberg blocks. Use @wordpress/components for consistent UI (buttons, forms)
developer.wordpress.org
. Leverage @wordpress/scripts for bundling: e.g. "build": "wp-scripts build" in package.json
developer.wordpress.org
. Create Gutenberg blocks for the booking form, service listings, and shortcodes (using block.json). Ensure all React components are well-documented and follow consistent coding conventions.
Booking System
Slot Logic & Calendars: Implement availability rules (single or multiple slots per service) and prevent double-booking. Sync with external calendars (Google Calendar, Outlook) by importing/exporting iCal feeds
wordpress.org
. For example, use Google Calendar API or allow users to import their *.ics. As in Booking Calendar plugin, support 2-way sync so that appointments in SchedSpot appear in user calendars
wordpress.org
wordpress.org
.
Geofencing: Use a map API (Google Maps, Mapbox) to allow service providers to set a service radius. At booking time, check client location vs provider’s service area. Store lat/long and compute distance to enforce geofence boundaries (e.g. using the Haversine formula).
SMS Confirmation: Integrate an SMS gateway (e.g. Twilio). On new or changed bookings, automatically send text confirmations/reminders. Many booking plugins (like Booknetic) use SMS to improve client contact
booknetic.com
. For example, when a client books, trigger Twilio->sendSMS(client_number, message) and similarly to the assigned worker. Include customizable SMS templates in settings.
User Dashboards
Provide distinct front-end interfaces (React apps or pages) based on user role: Client, Worker, and Admin. For example:
Client Dashboard: Allows customers to browse services, make new bookings, view/cancel upcoming appointments, and message providers. Show a calendar of their appointments. Enforce capability checks so only the client can view their own data.
Worker Dashboard: Allows service providers to manage their schedule: approve/decline requests, set availability, and view assigned jobs. Show real-time notifications (via REST or websockets) for new bookings. Employ WordPress role/capability checks so only a “worker” user sees this UI. Use the recommended pattern of separating admin/public code (e.g. conditional loading)
developer.wordpress.org
to ensure only workers load the worker dashboard scripts.
Admin Dashboard: In WP Admin, create custom pages (under the plugin menu) for managing users, content, and global settings. Use the Settings API
developer.wordpress.org
here (see below) and avoid mixing logic with presentation
developer.woocommerce.com
.
WooCommerce Integration
Activation Check: Ensure WooCommerce is active before hooking in (use is_plugin_active('woocommerce/woocommerce.php'))
developer.woocommerce.com
.
Payment Flow: Let clients pay via WooCommerce checkout. Create a WooCommerce product or order for each booking. On booking confirmation, automatically create a WC order (status pending) and redirect client to pay. Once paid, update booking status. Use WooCommerce webhooks or order status hooks to mark bookings as confirmed/complete.
Deposits & Invoices: Support partial payments/deposits. Integrate (or mimic) a WooCommerce deposits plugin: allow fixed or percentage deposits up front
wordpress.org
, with the balance due later. For example, add a custom “deposit” field to the checkout or use a compatible extension. Generate invoices via WooCommerce’s order emails. Optionally integrate a PDF invoice plugin (many free WooCommerce invoice plugins exist) so admins and clients have downloadable invoices.
Commissions: If the platform charges a commission, apply it in the order. For instance, if a client pays $100 and the commission is 10%, send $90 to the worker and keep $10 (via Stripe Connect or adjusting payouts). Use WooCommerce’s hooks to split payments or record earnings accordingly.
Messaging System
Implement an internal messaging (chat) feature:
Create a custom data model (e.g. a Message CPT or database table) with from_user_id, to_user_id, timestamp, and content.
On each booking detail page, include a React chat component so clients and workers can exchange messages (similar to TaskRabbit’s Q&A). Use REST API endpoints to send/receive messages.
Notify users of new messages via email or dashboard alerts. Ensure only the two participants (and admins) can read the thread.
Use WordPress capabilities (or custom endpoints) to secure messaging API.
Customization & Theming
Enable admins to fully brand the platform via:
Theme Settings: Provide a settings page (or Customizer section) for upload of logos, primary colors, and fonts. Save these options and enqueue a dynamic stylesheet or inline CSS to restyle the plugin’s front-end (buttons, headers) accordingly.
Layout Builder: Optionally include simple layout controls (e.g. drag-drop homepage blocks) so admins can rearrange modules (services grid, testimonials). Alternatively, rely on the theme’s page builder and provide Gutenberg blocks for SchedSpot content.
Appearance: Allow custom CSS via the admin panel for advanced theming. Ensure all frontend templates (booking form, calendar, emails) use templating conventions so they can be overridden in the theme if needed. (Following WooCommerce’s example of allowing template overrides
developer.woocommerce.com
is a good practice.)
Admin Settings
Use the WordPress Settings API to build the main control panel
developer.wordpress.org
. Key settings include:
System Fee & Commission: Numeric fields for platform fee percentage, tax settings, and options to enable/disable fees for specific services or roles.
User Management: Tables (with search) to list/edit Clients and Workers. Ability to approve or ban users. (Use WP_User functions or a custom user meta flag.)
Notifications: Checkboxes to enable email/SMS for events (new booking, cancellation, reminders). Set global email templates (using WP mail) for different booking statuses.
Analytics: Summaries of total bookings, revenue, top services, etc. You can use direct SQL queries or WooCommerce reports. For deeper analytics, integrate with a tool (e.g. Google Analytics events or a custom dashboard chart).
Ensure all settings pages follow WordPress’s look and feel (sections, nonce checks) via the Settings API
developer.wordpress.org
developer.wordpress.org
, which handles form submission, sanitization and security for you.
Testing & QA
PHP Unit Tests: Scaffold plugin unit tests with WP-CLI (wp scaffold plugin-tests schedspot)
make.wordpress.org
. Write PHPUnit tests for core classes and APIs. Use Travis CI/GitHub Actions to run tests on each commit
make.wordpress.org
.
Integration Tests: Use WP-CLI’s integration test setup (see WordPress CLI Handbook) to test database interactions and REST endpoints, treating them like integration tests
make.wordpress.org
.
JavaScript Tests: Use Jest or React Testing Library to unit-test React components and API calls.
Role-based Testing: Verify that each user role (Client, Worker, Admin) can only access their intended screens and APIs. Write scenarios (or automated tests with a tool like Cypress) to simulate a client booking an appointment, a worker accepting it, and payment processing.
Cross-Device QA: Test on various browsers/mobile. Ensure calendars and chat work responsively.
Documentation
Code Comments: Comment all classes and functions with PHPDoc (and JSDoc for JavaScript). This helps new developers quickly understand each component. Keep inline comments explaining non-obvious logic (e.g. custom SQL queries or hook filters).
README: Include a readme.txt following WordPress standards (plugin name, description, installation, FAQs)
developer.woocommerce.com
. Document dependencies, setup instructions, and contribution guidelines.
Developer Guide: Create a README_dev.md or wiki that maps out the plugin architecture: list custom post types, REST endpoints, React build process, and naming conventions. Describe where to find each module.
API Documentation: Publish REST API docs (e.g. via Swagger or a markdown file) that list routes and required parameters. Include example requests/responses. This ensures that future developers or integrations (like mobile apps) know how to use the API.
Future-Ready Extensions
Design the plugin modularly so that new features can be added without rewriting core code. Specifically:
Hooks & Filters: Apply WordPress actions/filters liberally (e.g. do_action('schedspot_booking_created', $booking)) so other plugins or custom code can modify behavior
developer.woocommerce.com
. For example, fire filters on pricing calculation, email content, or query parameters.
Modular Structure: Organize code by feature. Each major feature (e.g. Messaging, Booking, Payments) lives in its own class/file. Avoid monolithic functions (“God objects”)
developer.woocommerce.com
by breaking logic into small, testable pieces.
Plugin Hooks: Use well-named hooks at key integration points (booking saved, payment completed, message sent). Provide filter hooks to customize email templates or UI elements. This ensures that future modules (e.g. AI recommendation engine, mobile app integration, PWA support, Zapier webhooks) can plug in easily.
Decouple UI: Because the front-end is React-based with a REST backend, adding new UIs (like a mobile app or PWA) is easier; the same endpoints can serve those clients. As an example, exposing JSON endpoints for external integrations (Zapier, etc.) should be considered from the start.
Internationalization: Follow i18n standards (all strings in English as base)
developer.woocommerce.com
and load text domains in PHP and JS, so the plugin can be translated for any locale.
This structured roadmap – with clear modular tasks and references to best practices
developer.wordpress.org
developer.woocommerce.com
developer.wordpress.org
– will guide the development team (AI or human) through each phase. Each bullet and section is self-contained yet integrated, allowing new team members to jump in and understand context, purpose, and integration points immediately.